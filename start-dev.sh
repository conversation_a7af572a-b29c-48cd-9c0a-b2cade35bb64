#!/bin/bash

# OTC Hub Development Startup Script
# This script starts all services needed for local development

set -e

echo "🚀 Starting OTC Hub Development Environment"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_status "Checking requirements..."
    
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    if ! command -v yarn &> /dev/null; then
        print_warning "yarn is not installed. Installing yarn..."
        npm install -g yarn
    fi
    
    print_success "All requirements satisfied"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install contract dependencies
    print_status "Installing contract dependencies..."
    cd contracts
    npm install
    cd ..
    
    # Install backend dependencies
    print_status "Installing backend dependencies..."
    cd backend
    npm install
    cd ..
    
    # Install frontend dependencies
    print_status "Installing frontend dependencies..."
    cd frontend
    yarn install
    cd ..
    
    print_success "All dependencies installed"
}

# Setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # Copy environment files if they don't exist
    if [ ! -f "contracts/.env" ]; then
        if [ -f "contracts/.env.example" ]; then
            cp contracts/.env.example contracts/.env
            print_success "Created contracts/.env from example"
        else
            print_warning "contracts/.env.example not found"
        fi
    fi
    
    if [ ! -f "backend/.env" ]; then
        if [ -f "backend/.env.example" ]; then
            cp backend/.env.example backend/.env
            print_success "Created backend/.env from example"
        else
            print_warning "backend/.env.example not found"
        fi
    fi
    
    if [ ! -f "frontend/.env" ]; then
        if [ -f "frontend/.env.example" ]; then
            cp frontend/.env.example frontend/.env
            print_success "Created frontend/.env from example"
        else
            print_warning "frontend/.env.example not found"
        fi
    fi
}

# Compile contracts
compile_contracts() {
    print_status "Compiling smart contracts..."
    cd contracts
    npx hardhat compile
    cd ..
    print_success "Smart contracts compiled"
}

# Setup database
setup_database() {
    print_status "Setting up database..."
    cd backend
    
    # Generate Prisma client
    npx prisma generate
    print_success "Prisma client generated"
    
    # Check if database is accessible and run migrations
    if npx prisma migrate dev --name init 2>/dev/null; then
        print_success "Database migrations completed"
    else
        print_warning "Database migrations failed. Please ensure PostgreSQL is running and configured correctly."
    fi
    
    cd ..
}

# Start services
start_services() {
    print_status "Starting services..."
    
    # Start Hardhat network in background
    print_status "Starting Hardhat network..."
    cd contracts
    npx hardhat node &
    HARDHAT_PID=$!
    cd ..
    
    # Wait for Hardhat network to start
    sleep 5
    
    # Deploy contracts
    print_status "Deploying contracts..."
    cd contracts
    npx hardhat run scripts/deploy.ts --network localhost
    cd ..
    print_success "Contracts deployed"
    
    # Start backend in background
    print_status "Starting backend API..."
    cd backend
    npm run dev &
    BACKEND_PID=$!
    cd ..
    
    # Wait for backend to start
    sleep 3
    
    # Start frontend
    print_status "Starting frontend..."
    cd frontend
    yarn dev &
    FRONTEND_PID=$!
    cd ..
    
    print_success "All services started!"
    echo ""
    echo "🌐 Services are running:"
    echo "   - Hardhat Network: http://127.0.0.1:8545"
    echo "   - Backend API: http://localhost:3001"
    echo "   - Frontend: http://localhost:5173"
    echo ""
    echo "Press Ctrl+C to stop all services"
    
    # Wait for user interrupt
    trap 'kill $HARDHAT_PID $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit' INT
    wait
}

# Main execution
main() {
    check_requirements
    
    # Ask user what to do
    echo ""
    echo "What would you like to do?"
    echo "1) Full setup (install deps, setup env, compile, setup db, start services)"
    echo "2) Quick start (just start services)"
    echo "3) Install dependencies only"
    echo "4) Setup environment files only"
    echo "5) Compile contracts only"
    echo "6) Setup database only"
    echo ""
    read -p "Enter your choice (1-6): " choice
    
    case $choice in
        1)
            install_dependencies
            setup_environment
            compile_contracts
            setup_database
            start_services
            ;;
        2)
            start_services
            ;;
        3)
            install_dependencies
            ;;
        4)
            setup_environment
            ;;
        5)
            compile_contracts
            ;;
        6)
            setup_database
            ;;
        *)
            print_error "Invalid choice. Please run the script again."
            exit 1
            ;;
    esac
}

# Run main function
main
