@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }
  
  * {
    @apply border-gray-200;
  }
}

@layer components {
  /* Button Components */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 border-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-white border-gray-300 text-gray-700 hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 border-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 border-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 border-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-ghost {
    @apply btn bg-transparent border-transparent text-gray-600 hover:bg-gray-100 focus:ring-primary-500;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  /* Card Components */
  .card {
    @apply bg-white rounded-lg border shadow-sm;
  }
  
  .card-header {
    @apply px-6 py-4 border-b;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t bg-gray-50;
  }
  
  /* Form Components */
  .form-group {
    @apply space-y-1;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700;
  }
  
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .form-select {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .form-textarea {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .form-error {
    @apply text-sm text-danger-600;
  }
  
  /* Status Components */
  .status-badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-open {
    @apply status-badge bg-blue-100 text-blue-800;
  }
  
  .status-funded {
    @apply status-badge bg-yellow-100 text-yellow-800;
  }
  
  .status-settled {
    @apply status-badge bg-green-100 text-green-800;
  }
  
  .status-cancelled {
    @apply status-badge bg-gray-100 text-gray-800;
  }
  
  .status-disputed {
    @apply status-badge bg-red-100 text-red-800;
  }
  
  .status-active {
    @apply status-badge bg-green-100 text-green-800;
  }
  
  .status-inactive {
    @apply status-badge bg-gray-100 text-gray-800;
  }
  
  /* Loading Components */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
  
  .spinner-sm {
    @apply spinner h-4 w-4;
  }
  
  .spinner-md {
    @apply spinner h-6 w-6;
  }
  
  .spinner-lg {
    @apply spinner h-8 w-8;
  }
  
  /* Utility Classes */
  .text-mono {
    font-family: 'JetBrains Mono', monospace;
  }
  
  .truncate-address {
    @apply text-mono text-sm;
  }
  
  .gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .glass-effect {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }
}

@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}
