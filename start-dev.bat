@echo off
setlocal enabledelayedexpansion

:: OTC Hub Development Startup Script for Windows
:: This script starts all services needed for local development

echo.
echo 🚀 Starting OTC Hub Development Environment
echo ==========================================
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
)

:: Check if npm is installed
npm --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] npm is not installed. Please install npm first.
    pause
    exit /b 1
)

:: Check if yarn is installed
yarn --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] yarn is not installed. Installing yarn...
    npm install -g yarn
)

echo [INFO] All requirements satisfied
echo.

:: Ask user what to do
echo What would you like to do?
echo 1) Full setup (install deps, setup env, compile, start services)
echo 2) Quick start (just start services)
echo 3) Install dependencies only
echo 4) Setup environment files only
echo 5) Compile contracts only
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto full_setup
if "%choice%"=="2" goto quick_start
if "%choice%"=="3" goto install_deps
if "%choice%"=="4" goto setup_env
if "%choice%"=="5" goto compile_contracts
echo [ERROR] Invalid choice. Please run the script again.
pause
exit /b 1

:full_setup
call :install_dependencies
call :setup_environment
call :compile_contracts
call :start_services
goto end

:quick_start
call :start_services
goto end

:install_deps
call :install_dependencies
goto end

:setup_env
call :setup_environment
goto end

:compile_contracts
call :compile_contracts_only
goto end

:install_dependencies
echo [INFO] Installing dependencies...
echo.

echo [INFO] Installing contract dependencies...
cd contracts
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install contract dependencies
    pause
    exit /b 1
)
cd ..

echo [INFO] Installing backend dependencies...
cd backend
call npm install
if errorlevel 1 (
    echo [ERROR] Failed to install backend dependencies
    pause
    exit /b 1
)
cd ..

echo [INFO] Installing frontend dependencies...
cd frontend
call yarn install
if errorlevel 1 (
    echo [ERROR] Failed to install frontend dependencies
    pause
    exit /b 1
)
cd ..

echo [SUCCESS] All dependencies installed
echo.
goto :eof

:setup_environment
echo [INFO] Setting up environment files...

if not exist "contracts\.env" (
    if exist "contracts\.env.example" (
        copy "contracts\.env.example" "contracts\.env" >nul
        echo [SUCCESS] Created contracts\.env from example
    ) else (
        echo [WARNING] contracts\.env.example not found
    )
)

if not exist "backend\.env" (
    if exist "backend\.env.example" (
        copy "backend\.env.example" "backend\.env" >nul
        echo [SUCCESS] Created backend\.env from example
    ) else (
        echo [WARNING] backend\.env.example not found
    )
)

if not exist "frontend\.env" (
    if exist "frontend\.env.example" (
        copy "frontend\.env.example" "frontend\.env" >nul
        echo [SUCCESS] Created frontend\.env from example
    ) else (
        echo [WARNING] frontend\.env.example not found
    )
)

echo [SUCCESS] Environment setup completed
echo.
goto :eof

:compile_contracts_only
echo [INFO] Compiling smart contracts...
cd contracts
call npx hardhat compile
if errorlevel 1 (
    echo [ERROR] Failed to compile contracts
    pause
    exit /b 1
)
cd ..
echo [SUCCESS] Smart contracts compiled
echo.
goto :eof

:start_services
echo [INFO] Starting services...
echo.

:: Start Hardhat network
echo [INFO] Starting Hardhat network...
cd contracts
start "Hardhat Network" cmd /k "npx hardhat node"
cd ..

:: Wait for Hardhat network to start
echo [INFO] Waiting for Hardhat network to start...
timeout /t 5 /nobreak >nul

:: Deploy contracts
echo [INFO] Deploying contracts...
cd contracts
call npx hardhat run scripts/deploy.ts --network localhost
if errorlevel 1 (
    echo [ERROR] Failed to deploy contracts
    pause
    exit /b 1
)
cd ..
echo [SUCCESS] Contracts deployed

:: Start backend
echo [INFO] Starting backend API...
cd backend
start "Backend API" cmd /k "npm run dev"
cd ..

:: Wait for backend to start
echo [INFO] Waiting for backend to start...
timeout /t 3 /nobreak >nul

:: Start frontend
echo [INFO] Starting frontend...
cd frontend
start "Frontend" cmd /k "yarn dev"
cd ..

echo.
echo [SUCCESS] All services started!
echo.
echo 🌐 Services are running:
echo    - Hardhat Network: http://127.0.0.1:8545
echo    - Backend API: http://localhost:3001
echo    - Frontend: http://localhost:5173
echo.
echo Press any key to continue...
pause >nul
goto :eof

:end
echo.
echo Script completed!
pause
