import { type ClassValue, clsx } from 'clsx'
import { format, formatDistanceToNow, isValid } from 'date-fns'

/**
 * Utility function to combine class names
 */
export function cn(...inputs: ClassValue[]) {
  return clsx(inputs)
}

/**
 * Truncate Ethereum address for display
 */
export function truncateAddress(address: string, startLength = 6, endLength = 4): string {
  if (!address) return ''
  if (address.length <= startLength + endLength) return address
  return `${address.slice(0, startLength)}...${address.slice(-endLength)}`
}

/**
 * Format token amount with proper decimals
 */
export function formatTokenAmount(
  amount: string | number | bigint,
  decimals = 18,
  displayDecimals = 4
): string {
  try {
    const value = typeof amount === 'bigint' ? amount : BigInt(amount.toString())
    const divisor = BigInt(10 ** decimals)
    const quotient = value / divisor
    const remainder = value % divisor
    
    if (remainder === 0n) {
      return quotient.toString()
    }
    
    const decimalPart = remainder.toString().padStart(decimals, '0')
    const trimmedDecimal = decimalPart.slice(0, displayDecimals).replace(/0+$/, '')
    
    return trimmedDecimal ? `${quotient}.${trimmedDecimal}` : quotient.toString()
  } catch (error) {
    console.error('Error formatting token amount:', error)
    return '0'
  }
}

/**
 * Parse token amount to wei
 */
export function parseTokenAmount(amount: string, decimals = 18): bigint {
  try {
    if (!amount || amount === '0') return 0n
    
    const [whole, decimal = ''] = amount.split('.')
    const paddedDecimal = decimal.padEnd(decimals, '0').slice(0, decimals)
    const combined = whole + paddedDecimal
    
    return BigInt(combined)
  } catch (error) {
    console.error('Error parsing token amount:', error)
    return 0n
  }
}

/**
 * Format currency amount
 */
export function formatCurrency(
  amount: string | number,
  currency = 'USD',
  locale = 'en-US'
): string {
  try {
    const value = typeof amount === 'string' ? parseFloat(amount) : amount
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    }).format(value)
  } catch (error) {
    console.error('Error formatting currency:', error)
    return `${amount} ${currency}`
  }
}

/**
 * Format percentage
 */
export function formatPercentage(value: number, decimals = 2): string {
  return `${(value * 100).toFixed(decimals)}%`
}

/**
 * Format date for display
 */
export function formatDate(date: string | Date, formatStr = 'MMM dd, yyyy HH:mm'): string {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    if (!isValid(dateObj)) return 'Invalid date'
    return format(dateObj, formatStr)
  } catch (error) {
    console.error('Error formatting date:', error)
    return 'Invalid date'
  }
}

/**
 * Format relative time
 */
export function formatRelativeTime(date: string | Date): string {
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    if (!isValid(dateObj)) return 'Invalid date'
    return formatDistanceToNow(dateObj, { addSuffix: true })
  } catch (error) {
    console.error('Error formatting relative time:', error)
    return 'Invalid date'
  }
}

/**
 * Generate agreement hash for trade
 */
export function generateAgreementHash(data: {
  offerId: number
  quantity: string
  timestamp: number
  taker: string
}): string {
  const message = `${data.offerId}-${data.quantity}-${data.timestamp}-${data.taker}`
  // In a real implementation, you might want to use a proper hashing library
  // For now, we'll create a simple hash
  let hash = 0
  for (let i = 0; i < message.length; i++) {
    const char = message.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // Convert to 32-bit integer
  }
  return `0x${Math.abs(hash).toString(16).padStart(64, '0')}`
}

/**
 * Validate Ethereum address
 */
export function isValidAddress(address: string): boolean {
  return /^0x[a-fA-F0-9]{40}$/.test(address)
}

/**
 * Validate positive number
 */
export function isValidPositiveNumber(value: string): boolean {
  const num = parseFloat(value)
  return !isNaN(num) && num > 0
}

/**
 * Copy text to clipboard
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch (error) {
    console.error('Failed to copy to clipboard:', error)
    return false
  }
}

/**
 * Debounce function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Sleep utility
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

/**
 * Get status color class
 */
export function getStatusColor(status: string): string {
  const colorMap: Record<string, string> = {
    Open: 'text-blue-600 bg-blue-100',
    Funded: 'text-yellow-600 bg-yellow-100',
    Settled: 'text-green-600 bg-green-100',
    Cancelled: 'text-gray-600 bg-gray-100',
    Disputed: 'text-red-600 bg-red-100',
    AdminClosed: 'text-purple-600 bg-purple-100',
    ACTIVE: 'text-green-600 bg-green-100',
    INACTIVE: 'text-gray-600 bg-gray-100',
    FILLED: 'text-blue-600 bg-blue-100',
  }
  return colorMap[status] || 'text-gray-600 bg-gray-100'
}

/**
 * Calculate trade total
 */
export function calculateTradeTotal(quantity: string, price: string): string {
  try {
    const qty = parseFloat(quantity)
    const prc = parseFloat(price)
    if (isNaN(qty) || isNaN(prc)) return '0'
    return (qty * prc).toFixed(6)
  } catch (error) {
    return '0'
  }
}

/**
 * Format large numbers
 */
export function formatLargeNumber(num: number): string {
  if (num >= 1e9) return (num / 1e9).toFixed(1) + 'B'
  if (num >= 1e6) return (num / 1e6).toFixed(1) + 'M'
  if (num >= 1e3) return (num / 1e3).toFixed(1) + 'K'
  return num.toString()
}

/**
 * Get explorer URL for transaction
 */
export function getExplorerUrl(txHash: string, chainId = 31337): string {
  const explorers: Record<number, string> = {
    1: 'https://etherscan.io/tx/',
    11155111: 'https://sepolia.etherscan.io/tx/',
    31337: '#', // Local network
  }
  return `${explorers[chainId] || '#'}${txHash}`
}
