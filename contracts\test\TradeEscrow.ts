
import { expect } from "chai";
import { ethers } from "hardhat";
import { Contract, Signer, ContractTransactionReceipt } from "ethers";
import { TradeEscrow, TradeFactory, MockERC20 } from "../typechain-types";

describe("TradeEscrow and TradeFactory", function () {
    let seller: Signer, buyer: Signer, admin: Signer;
    let depositToken: MockERC20;
    let tradeFactory: TradeFactory;

    const price = ethers.parseUnits("100", 18);
    const deposit = ethers.parseUnits("50", 18);

    beforeEach(async function () {
        [, seller, buyer, admin] = await ethers.getSigners();

        const MockERC20Factory = await ethers.getContractFactory("MockERC20");
        depositToken = await MockERC20Factory.deploy(ethers.parseUnits("1000", 18));
        
        const sellerAddress = await seller.getAddress();
        const buyerAddress = await buyer.getAddress();

        await depositToken.transfer(sellerAddress, ethers.parseUnits("100", 18));
        await depositToken.transfer(buyerAddress, ethers.parseUnits("200", 18));

        const TradeFactoryFactory = await ethers.getContractFactory("TradeFactory");
        tradeFactory = await TradeFactoryFactory.deploy(await admin.getAddress());
    });

    async function createAndFundTrade(): Promise<TradeEscrow> {
        const sellerAddress = await seller.getAddress();
        const buyerAddress = await buyer.getAddress();
        const tokenAddress = await depositToken.getAddress();

        const createTx = await tradeFactory.connect(seller).createTrade(buyerAddress, tokenAddress, price, deposit);
        const receipt = await createTx.wait() as ContractTransactionReceipt;

        const logs = receipt.logs;
        const event = logs.find(log => log.eventName === 'TradeCreated');
        const escrowAddress = event.args.escrowContract;
        const tradeEscrow = await ethers.getContractAt("TradeEscrow", escrowAddress) as TradeEscrow;

        await depositToken.connect(seller).approve(escrowAddress, deposit);
        await depositToken.connect(buyer).approve(escrowAddress, price + deposit);

        await tradeEscrow.connect(seller).fund();
        await tradeEscrow.connect(buyer).fund();

        return tradeEscrow;
    }

    it("Should handle a successful trade from creation to settlement", async function () {
        const sellerAddress = await seller.getAddress();
        const buyerAddress = await buyer.getAddress();

        const sellerBalanceBeforeTrade = await depositToken.balanceOf(sellerAddress);
        const buyerBalanceBeforeTrade = await depositToken.balanceOf(buyerAddress);

        const tradeEscrow = await createAndFundTrade();
        const escrowAddress = await tradeEscrow.getAddress();

        expect(await tradeEscrow.currentStatus()).to.equal(1); // 1 = Funded

        const totalInEscrow = price + deposit * 2n;
        expect(await depositToken.balanceOf(escrowAddress)).to.equal(totalInEscrow);

        await tradeEscrow.connect(seller).confirm();
        await tradeEscrow.connect(buyer).confirm();

        expect(await tradeEscrow.currentStatus()).to.equal(2); // 2 = Closed

        const sellerPayout = price + deposit;
        const buyerRefund = deposit;

        const expectedSellerFinalBalance = sellerBalanceBeforeTrade - deposit + sellerPayout;
        const expectedBuyerFinalBalance = buyerBalanceBeforeTrade - (price + deposit) + buyerRefund;

        expect(await depositToken.balanceOf(sellerAddress)).to.equal(expectedSellerFinalBalance);
        expect(await depositToken.balanceOf(buyerAddress)).to.equal(expectedBuyerFinalBalance);
        expect(await depositToken.balanceOf(escrowAddress)).to.equal(0);
    });

    it("Should allow the admin to withdraw funds in a dispute", async function () {
        const tradeEscrow = await createAndFundTrade();
        const escrowAddress = await tradeEscrow.getAddress();
        const adminAddress = await admin.getAddress();

        const initialAdminBalance = await depositToken.balanceOf(adminAddress);
        const totalInEscrow = await depositToken.balanceOf(escrowAddress);

        await tradeEscrow.connect(admin).adminWithdraw();

        expect(await tradeEscrow.currentStatus()).to.equal(2); // 2 = Closed

        expect(await depositToken.balanceOf(escrowAddress)).to.equal(0);
        expect(await depositToken.balanceOf(adminAddress)).to.equal(initialAdminBalance + totalInEscrow);
    });

    it("Should fail if a non-participant tries to confirm", async function () {
        const tradeEscrow = await createAndFundTrade();
        const nonParticipant = (await ethers.getSigners())[0];
        await expect(tradeEscrow.connect(nonParticipant).confirm()).to.be.revertedWith("Escrow: Only participants can confirm");
    });

    it("Should fail if admin tries to withdraw from a non-funded contract", async function () {
        const sellerAddress = await seller.getAddress();
        const buyerAddress = await buyer.getAddress();
        const tokenAddress = await depositToken.getAddress();

        const createTx = await tradeFactory.connect(seller).createTrade(buyerAddress, tokenAddress, price, deposit);
        const receipt = await createTx.wait() as ContractTransactionReceipt;
        
        const logs = receipt.logs;
        const event = logs.find(log => log.eventName === 'TradeCreated');
        const escrowAddress = event.args.escrowContract;
        const tradeEscrow = await ethers.getContractAt("TradeEscrow", escrowAddress);

        await expect(tradeEscrow.connect(admin).adminWithdraw()).to.be.revertedWith("Escrow: Invalid status for this action");
    });
});
